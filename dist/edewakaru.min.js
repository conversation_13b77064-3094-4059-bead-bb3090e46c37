// ==UserScript==
// @name         「絵でわかる日本語」閲覧体験強化
// @namespace    http://tampermonkey.net/
// @description  「絵でわかる日本語」サイトの漢字の読み方を括弧書きからルビ表示に自動変換し、広告や不要な要素を非表示にすることで、快適な読書環境を提供します。
// @icon         https://livedoor.blogimg.jp/edewakaru/imgs/8/c/8cdb7924.png
// @match        https://www.edewakaru.com/*
// <AUTHOR>
// @version      2025.07.12
// @grant        GM_addStyle
// @grant        GM_getValue
// @grant        GM_setValue
// @run-at       document-start
// ==/UserScript==

;(function () {
  ;('use strict')
  const RULES = {
    STANDARD_WORDS: ['合（あ）', '青い色（あおいいろ）', '足が早い（あしがはやい）', '当たり前（あたりまえ）', '甘い物（あまいもの）', '雨の日（あめのひ）', '歩（ある）', '行き方（いきかた）', '行き渡る（いきわたる）', '５日間（いつかかん）', '以上（いじょう）', '１回（いっかい）', '１か月（いっかげつ）', '１か月間（いっかげつかん）', '１杯（いっぱい）', '１泊（いっぱく）', '駅（えき）', '落ち着（おちつ）', 'お茶する（おちゃする）', '思い出す（おもいだす）', '折に触れて（おりにふれて）', '折も折（おりもおり）', '折を見て（おりをみて）', '折があれば（おりがあれば）', 'お団子（おだんご）', '買い物（かいもの）', '貸し借り（かしかり）', '髪の毛（かみのけ）', '唐揚げ（からあげ）', '考え方（かんがえかた）', '感じ方（かんじかた）', '聞き手（ききて）', '気持ち（きもち）', '口の中（くちのなか）', '触り心地（さわりごこち）', '三分の一（さんぶんのいち）', '試験（しけん）', '使用（しよう）', '出張（しゅっちょう）', '昭和の日（しょうわのひ）', '住（す）', '座り心地（すわりごこち）', '経（た）', '貯（た）', '立ち読み（たちよみ）', '楽（たの）', '食べ物（たべもの）', '食べず嫌い（たべずぎらい）', '遅刻（ちこく）', '使い方（つかいかた）', '使い分け（つかいわけ）', '届け出（とどけで）', '夏休み中（なつやすみちゅう）', '悩み事（なやみごと）', '長い間（ながいあいだ）', '入学（にゅうがく）', '日記（にっき）', '盗み食い（ぬすみぐい）', '寝（ね）', '寝る前（ねるまえ）', '入（はい）', '吐き気（はきけ）', '話し手（はなして）', '腹が立つ（はらがたつ）', '一つ（ひとつ）', '独り言（ひとりごと）', '星の数（ほしのかず）', '星の数ほどある（ほしのかずほどある）', '星の数ほどいる（ほしのかずほどいる）', '待（ま）', '前（まえ）', '万引き（まんびき）', '窓の外（まどのそと）', '真っ暗（まっくら）', '味覚 （みかく）', '申し訳（もうしわけ）', '元カレ（もとかれ）', '有名（ゆうめい）', '夕ご飯（ゆうごはん）', '呼び方（よびかた）', '０点（れいてん）', '連続（れんぞく）', '暮（ぐ）', '残業（ざんぎょう）', '自身（じしん）', '大好き（だいすき）', '動作（どうさ）', '通（どお）', '文法（ぶんぽう）', 'アタック（attack）', 'm（メートル）'],
    FORCED_READING: [
      { pattern: '羽根を伸ばす（羽根を伸ばす）', reading: 'はねをのばす' },
      { pattern: '長蛇の列（長蛇の列）', reading: 'ちょうだのれつ' },
      { pattern: '付き合（つきあい）', reading: 'つきあ' },
    ],
    FORCED_REPLACEMENT: [
      { pattern: '目に余る②（めにあまる）', replacement: '<ruby>目<rt>め</rt></ruby>に<ruby>余<rt>あま</rt></ruby>る②' },
      { pattern: '言い方（いいかた）', replacement: '<ruby>言<rt>い</rt></ruby>い<ruby>方<rt>かた</rt></ruby>' },
      { pattern: '言い訳（いいわけ）', replacement: '<ruby>言<rt>い</rt></ruby>い<ruby>訳<rt>わけ</rt></ruby>' },
      { pattern: '原因・理由（げんいん・りゆう）', replacement: '<ruby>原因<rt>げんいん</rt></ruby>・<ruby>理由<rt>りゆう</rt></ruby>' },
      { pattern: '目の色が変わる・目の色を変える（めのいろがかわる・かえる）', replacement: '<ruby>目<rt>め</rt></ruby>の<ruby>色<rt>いろ</rt></ruby>が<ruby>変<rt>かわ</rt></ruby>る・<ruby>目<rt>め</rt></ruby>の<ruby>色<rt>いろ</rt></ruby>を<ruby>変<rt>かえ</rt></ruby>える' },
      { pattern: '水の泡になる・水の泡となる（みずのあわになる）', replacement: '<ruby>水<rt>みず</rt></ruby>の<ruby>泡<rt>あわ</rt></ruby>になる・<ruby>水<rt>みず</rt></ruby>の<ruby>泡<rt>あわ</rt></ruby>となる' },
      { pattern: '意味で（いみ）', replacement: '<ruby>意味<rt>いみ</rt></ruby>で' },
      { pattern: '和製英語で（わせいえいご）', replacement: '<ruby>和製英語<rt>わせいえいご</rt></ruby>で' },
      { pattern: '財布を（さいふ）', replacement: '<ruby>財布<rt>さいふ</rt></ruby>を' },
    ],
    HTML_REPLACEMENT_RULES: [
      { pattern: /一瞬（いっしゅん<br>）/g, replacement: '<ruby>一瞬<rt>いっしゅん</rt></ruby>' },
      { pattern: /<b><span style="font-size: 125%;">居<\/span><\/b>（い）/g, replacement: '<b><ruby>居<rt>い</rt></ruby></b>' },
      { pattern: /<b style="font-size: large;">留守<\/b>（るす）/g, replacement: '<b><ruby>留守<rt>るす</rt></ruby></b>' },
    ],
    ALWAYS_EXCLUDE: new Set(['挙句（に）', '道草（を）', '以上（は）', '人称（私）', '人称（あなた）', '矢先（に）']),
    RUBY_EXCLUDE_PARTICLES: new Set(['に', 'は', 'を', 'が', 'の', 'と', 'で', 'から', 'まで', 'へ', 'も', 'や', 'ね', 'よ', 'さ']),
  }
  const PageOptimizer = {
    _config: {
      GLOBAL_REMOVE_SELECTORS: ['header#blog-header', 'footer#blog-footer', '.ldb_menu', '.article-social-btn', '.adsbygoogle', 'a[href*="blogmura.com"]', 'a[href*="with2.net"]'],
      INITIAL_PAGE_STYLES: `
        #container { width: 100%; } @media (min-width: 960px) { #container { max-width: 960px; } } @media (min-width: 1040px) { #container { max-width: 1040px; } }
        #content { display: flex; position: relative; padding: 50px 0 !important; } #main { flex: 1; float: none !important; width: 100% !important; }
        aside#sidebar { visibility: hidden; float: none !important; width: 350px !important; flex: 0 0 350px; }
        .plugin-categorize { position: fixed; height: 85vh; display: flex; flex-direction: column; padding: 0 !important; width: 350px !important; } .plugin-categorize .side { flex: 1; overflow-y: auto; max-height: unset; } .plugin-categorize .side > :not([hidden]) ~ :not([hidden]) { margin-top: 5px; margin-bottom: 0; }
        .article { padding: 0 0 20px 0 !important; margin-bottom: 30px !important; } .article-body { padding: 0 !important; } .article-pager { margin-bottom: 0 !important; }
        .article-body-inner { line-height: 2; opacity: 0; transition: opacity 0.3s; } .article-body-inner img.pict { margin: 0 !important; width: 80% !important; display: block; } .article-body-inner strike { color: orange; } .article-body-inner iframe { margin: 4px 0 !important; }
        .to-pagetop { position: fixed; bottom: 1.2rem; right: 220px; z-index: 1000; }
        rt, iframe, .pager { -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; }
        header#blog-header, footer#blog-footer, .ldb_menu, .article-social-btn, .adsbygoogle, #ldblog_related_articles_01d4ecf1, #ad2 { display: none !important; }
        .article-body-inner:after, .article-meta:after, #container:after, #content:after, article:after, section:after, .cf:after { content: none !important; display: none !important; height: auto !important; visibility: visible !important; }
      `,
    },
    init() {
      GM_addStyle(this._config.INITIAL_PAGE_STYLES)
      this._removeGlobalElements()
    },
    _removeGlobalElements() {
      document.querySelectorAll(this._config.GLOBAL_REMOVE_SELECTORS.join(',')).forEach((el) => el.remove())
    },
    cleanupArticleBody(container) {
      const elementsToRemove = [...container.querySelectorAll('a[href*="blogmura.com"], a[href*="with2.net"], script')]
      const adDiv = container.querySelector('#ad2')
      if (adDiv) {
        let next = adDiv.nextElementSibling
        while (next) {
          elementsToRemove.push(next)
          next = next.nextElementSibling
        }
        elementsToRemove.push(adDiv)
      }
      elementsToRemove.forEach((el) => el.remove())
      this._trimContainerBreaks(container)
      container.style.opacity = 1
    },
    _trimContainerBreaks(container) {
      const isWhitespaceNode = (node) => !node || (node.nodeType === 3 && /^\s*$/.test(node.textContent)) || (node.nodeType === 1 && (node.tagName === 'BR' || (node.tagName === 'SPAN' && node.innerHTML === ' ')))
      while (isWhitespaceNode(container.firstChild)) container.removeChild(container.firstChild)
      while (isWhitespaceNode(container.lastChild)) container.removeChild(container.lastChild)
    },
    finalizeLayout() {
      const sidebar = document.querySelector('aside#sidebar')
      if (!sidebar) return
      const category = sidebar.querySelector('.plugin-categorize')
      sidebar.textContent = ''
      if (category) {
        sidebar.appendChild(category)
        sidebar.style.visibility = 'visible'
      }
    },
  }
  const ImageProcessor = {
    _config: {
      IMG_SRC_REGEX: /(https:\/\/livedoor\.blogimg\.jp\/edewakaru\/imgs\/[a-z0-9]+\/[a-z0-9]+\/[a-z0-9]+)-s(\.jpg)/i,
    },
    process(container) {
      container.querySelectorAll('a[href*="livedoor.blogimg.jp"]').forEach((link) => {
        const img = link.querySelector('img.pict')
        if (!img) return
        const newImg = document.createElement('img')
        newImg.src = img.src.replace(this._config.IMG_SRC_REGEX, '$1$2')
        newImg.alt = (img.alt || '').replace(/blog/gi, '')
        Object.assign(newImg, { className: img.className, width: img.width, height: img.height })
        link.replaceWith(newImg)
      })
    },
  }
  const IframeLazyLoader = {
    _config: {
      IFRAME_SELECTOR: 'iframe[src*="richlink.blogsys.jp"]',
      ROOT_MARGIN: '200px 0px',
      LAZY_LOAD_CLASS: 'lazy-load-iframe-placeholder',
      CLICK_LOAD_CLASS: 'click-to-load-iframe-placeholder',
      STYLES: `
        .lazy-load-iframe-placeholder { display: inline-block; vertical-align: top; background-color: #fafafa; box-sizing: border-box; margin: 4px 0; background-image: url('data:image/svg+xml;charset=utf8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20100%20100%22%20preserveAspectRatio%3D%22xMidYMid%22%20width%3D%2240%22%20height%3D%2240%22%20style%3D%22shape-rendering%3A%20auto%3B%20background%3A%20none%3B%22%3E%3Ccircle%20cx%3D%2250%22%20cy%3D%2250%22%20r%3D%2232%22%20stroke-width%3D%228%22%20stroke%3D%22%23cccccc%22%20stroke-dasharray%3D%2250.26548245743669%2050.26548245743669%22%20fill%3D%22none%22%20stroke-linecap%3D%22round%22%3E%3CanimateTransform%20attributeName%3D%22transform%22%20type%3D%22rotate%22%20repeatCount%3D%22indefinite%22%20dur%3D%221s%22%20keyTimes%3D%220%3B1%22%20values%3D%220%2050%2050%3B360%2050%2050%22%3E%3C%2FanimateTransform%3E%3C%2Fcircle%3E%3C%2Fsvg%3E'); background-repeat: no-repeat; background-position: center; }
        .click-to-load-iframe-placeholder { opacity: 0.8; display: inline-grid; place-items: center; vertical-align: top; border: 1px solid #e5e5e5; color: #888; background-color: #fafafa; font-weight: bold; font-size: 14px; cursor: pointer; transition: background-color 0.2s, color 0.2s, border-color 0.2s; box-sizing: border-box; margin: 4px 0; }
        .click-to-load-iframe-placeholder:hover { opacity: 0.8; border-color: #3B82F6; color: #3B82F6; background-color: #f4f8ff; }
        @media screen and (max-width: 870px) { .lazy-load-iframe-placeholder, .click-to-load-iframe-placeholder { max-width: 350px !important; height: 105px !important; margin-bottom:19px; } }
        @media screen and (min-width: 871px) { .lazy-load-iframe-placeholder, .click-to-load-iframe-placeholder { max-width: 580px !important; height: 120px !important; } }
      `,
    },
    _observer: null,
    _isSupported: true,
    init() {
      GM_addStyle(this._config.STYLES)
      if (!('IntersectionObserver' in window)) {
        this._isSupported = false
        console.warn('IntersectionObserver not supported.')
        return
      }
      if (SettingsPanel.isLazyLoadEnabled()) {
        this._observer = new IntersectionObserver(
          (entries, observer) => {
            entries.forEach((entry) => {
              if (entry.isIntersecting) {
                const iframe = entry.target
                const src = iframe.dataset.src
                const originalStyle = iframe.dataset.originalStyle
                if (src) {
                  iframe.onload = () => {
                    iframe.classList.remove(this._config.LAZY_LOAD_CLASS)
                    iframe.onload = null
                  }
                  iframe.setAttribute('style', originalStyle)
                  iframe.src = src
                  iframe.removeAttribute('data-src')
                  iframe.removeAttribute('data-original-style')
                }
                observer.unobserve(iframe)
              }
            })
          },
          { rootMargin: this._config.ROOT_MARGIN },
        )
      }
    },
    processContainer(container) {
      const iframes = container.querySelectorAll(this._config.IFRAME_SELECTOR)
      if (iframes.length === 0) return
      SettingsPanel.isLazyLoadEnabled() ? this._processForLazyLoad(iframes) : this._processForClickToLoad(iframes)
    },
    _processForLazyLoad(iframes) {
      if (!this._isSupported || !this._observer) return
      iframes.forEach((iframe) => {
        const originalSrc = iframe.getAttribute('src')
        if (originalSrc && originalSrc !== 'about:blank' && !iframe.hasAttribute('data-src')) {
          const originalStyle = iframe.getAttribute('style') || ''
          iframe.dataset.originalStyle = originalStyle
          iframe.dataset.src = originalSrc
          iframe.src = 'about:blank'
          iframe.classList.add(this._config.LAZY_LOAD_CLASS)
          this._observer.observe(iframe)
        }
      })
    },
    _processForClickToLoad(iframes) {
      iframes.forEach((iframe) => {
        if (iframe.parentElement.classList.contains(this._config.CLICK_LOAD_CLASS)) return
        const originalSrc = iframe.src
        const originalStyle = iframe.getAttribute('style') || ''
        const placeholder = document.createElement('div')
        placeholder.className = this._config.CLICK_LOAD_CLASS
        placeholder.textContent = '▶ 関連コンテンツを表示'
        placeholder.setAttribute('style', originalStyle)
        placeholder.addEventListener(
          'click',
          () => {
            const newIframe = document.createElement('iframe')
            newIframe.src = originalSrc
            newIframe.setAttribute('style', originalStyle)
            newIframe.setAttribute('frameborder', '0')
            newIframe.setAttribute('scrolling', 'no')
            placeholder.replaceWith(newIframe)
          },
          { once: true },
        )
        iframe.replaceWith(placeholder)
      })
    },
  }
  const RubyConverter = {
    _config: null,
    _regex: {
      bracket: /[【「](?:.*?)([^【】「」（）・、\s～〜]+)（([^（）]*)）([^【】「」（）]*)[】」]/g,
      katakana: /([ァ-ンー]+)[（(]([\w\s+]+)[）)]/g,
      ruby: /([一-龯々]+)\s*（([^（）]*)）/g,
      kanaOnly: /^[\u3040-\u309F]+$/,
      nonKana: /[^\u3040-\u309F]/,
      isKanaChar: /^[\u3040-\u309F]$/,
      hasInvalidChars: /[^一-龯々\u3040-\u309F\u30A0-\u30FF]/,
    },
    _processedWords: { patternResults: new Map(), globalRegex: null },
    _dynamicWords: new Set(),
    init(config) {
      this._config = config
      this._preprocessWords(config)
    },
    processContainer(container) {
      this._applyHtmlReplacements(container)
      this._findAndRegisterCompounds(container)
      this._processRubyInNodes(container)
    },
    _escapeRegExp: (string) => string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'),
    _parseCompoundEntry(entry) {
      if (typeof entry === 'string') {
        const match = entry.match(/(.*?)（(.*?)）/)
        if (match) return { pattern: entry, kanji: match[1].trim(), reading: match[2] }
      } else if (entry.reading) {
        return { pattern: entry.pattern, kanji: entry.pattern.replace(/（.*?）/, ''), reading: entry.reading }
      } else if (entry.replacement) {
        return entry
      }
      return null
    },
    _preprocessWords(config) {
      const allPatterns = []
      config.STANDARD_WORDS.forEach((entry) => {
        const match = entry.match(/(.*?)（(.*?)）/)
        if (!match) return
        const pattern = entry
        const kanji = match[1].trim()
        const reading = match[2]
        allPatterns.push(this._escapeRegExp(pattern))
        this._processedWords.patternResults.set(pattern, this._segmentCompoundWord(kanji, reading))
      })
      config.FORCED_READING.forEach((entry) => {
        const { pattern, reading } = entry
        const kanji = pattern.replace(/（.*?）/, '')
        allPatterns.push(this._escapeRegExp(pattern))
        this._processedWords.patternResults.set(pattern, this._segmentCompoundWord(kanji, reading))
      })
      config.FORCED_REPLACEMENT.forEach((entry) => {
        const { pattern, replacement } = entry
        allPatterns.push(this._escapeRegExp(pattern))
        this._processedWords.patternResults.set(pattern, replacement)
      })
      this._rebuildGlobalRegex(allPatterns)
    },
    _rebuildGlobalRegex(patterns) {
      this._processedWords.globalRegex = patterns.length > 0 ? new RegExp(`(${patterns.join('|')})`, 'g') : null
    },
    _segmentCompoundWord(kanji, reading) {
      let result = '',
        kanjiIndex = 0,
        readingIndex = 0
      while (kanjiIndex < kanji.length) {
        if (this._regex.isKanaChar.test(kanji[kanjiIndex])) {
          result += kanji[kanjiIndex]
          readingIndex = reading.indexOf(kanji[kanjiIndex], readingIndex) + 1
          kanjiIndex++
        } else {
          let kanjiPart = ''
          while (kanjiIndex < kanji.length && !this._regex.isKanaChar.test(kanji[kanjiIndex])) {
            kanjiPart += kanji[kanjiIndex++]
          }
          const nextKanaIndex = kanjiIndex < kanji.length ? reading.indexOf(kanji[kanjiIndex], readingIndex) : reading.length
          result += `<ruby>${kanjiPart}<rt>${reading.substring(readingIndex, nextKanaIndex)}</rt></ruby>`
          readingIndex = nextKanaIndex
        }
      }
      return result
    },
    _processTextContent(text) {
      if (!text.includes('（') && !text.includes('(')) return text
      if (this._processedWords.globalRegex) {
        text = text.replace(this._processedWords.globalRegex, (match) => this._processedWords.patternResults.get(match) || match)
      }
      text = text.replace(this._regex.katakana, (_, katakana, romaji) => `<ruby>${katakana}<rt>${romaji}</rt></ruby>`)
      return text.replace(this._regex.ruby, (match, kanji, reading) => {
        const fullMatch = `${kanji}（${reading}）`
        if (this._config.ALWAYS_EXCLUDE.has(fullMatch) || (this._config.RUBY_EXCLUDE_PARTICLES.has(reading) && this._regex.kanaOnly.test(kanji)) || this._regex.nonKana.test(reading)) {
          return match
        }
        return reading ? `<ruby>${kanji}<rt>${reading}</rt></ruby>` : match
      })
    },
    _applyHtmlReplacements(element) {
      let html = element.innerHTML
      this._config.HTML_REPLACEMENT_RULES.forEach((rule) => {
        html = html.replace(rule.pattern, rule.replacement)
      })
      if (html !== element.innerHTML) element.innerHTML = html
    },
    _findAndRegisterCompounds(element) {
      let match,
        newPatterns = []
      const html = element.innerHTML
      while ((match = this._regex.bracket.exec(html)) !== null) {
        const kanjiPart = match[1]
        const readingPart = match[2]
        const suffixPart = match[3]
        if (match[0].includes('＋') || match[0].includes('+') || this._regex.nonKana.test(readingPart) || this._regex.hasInvalidChars.test(kanjiPart)) {
          continue
        }
        const fullPattern = `${kanjiPart}（${readingPart}）${suffixPart}`
        if (!this._dynamicWords.has(fullPattern)) {
          this._dynamicWords.add(fullPattern)
          const rubyHtml = this._segmentCompoundWord(kanjiPart, readingPart) + suffixPart
          this._processedWords.patternResults.set(fullPattern, rubyHtml)
          newPatterns.push(this._escapeRegExp(fullPattern))
        }
      }
      if (newPatterns.length > 0) {
        const existing = this._processedWords.globalRegex ? this._processedWords.globalRegex.source.slice(1, -2).split('|') : []
        this._rebuildGlobalRegex([...existing, ...newPatterns])
      }
    },
    _processRubyInNodes(root) {
      const walker = document.createTreeWalker(root, NodeFilter.SHOW_TEXT, {
        acceptNode: (n) => (n.parentNode.nodeName !== 'SCRIPT' && n.parentNode.nodeName !== 'STYLE' ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_REJECT),
      })
      const nodesToProcess = []
      let node
      while ((node = walker.nextNode())) {
        const newContent = this._processTextContent(node.nodeValue)
        if (newContent !== node.nodeValue) nodesToProcess.push({ node, newContent })
      }
      for (let i = nodesToProcess.length - 1; i >= 0; i--) {
        const { node, newContent } = nodesToProcess[i]
        node.parentNode.replaceChild(document.createRange().createContextualFragment(newContent), node)
      }
    },
  }
  const SettingsPanel = {
    _config: {
      KEYS: { SCRIPT_ENABLED: 'ruby_converter_enabled', FURIGANA_VISIBLE: 'furigana_visible', IFRAME_LAZY_LOAD: 'iframe_lazy_load' },
      STYLES: `
        #settings-panel { position: fixed; bottom: 1.5rem; right: 1.5rem; z-index: 9999; display: flex; flex-direction: column; gap: 0.5rem; padding: 1rem; background: white; border-radius: 4px; box-shadow: 0 10px 15px -3px rgba(0,0,0,0.1),0 4px 6px -2px rgba(0,0,0,0.05); width: 140px; opacity: 0.8; user-select: none; } .settings-title { font-size: 0.875rem; font-weight: 600; color: #1F2937; margin: 0 0 0.375rem 0; text-align: center; border-bottom: 1px solid #E5E7EB; padding-bottom: 0.375rem; }
        .setting-item { display: flex; align-items: center; justify-content: space-between; gap: 0.5rem; } .setting-label { font-size: 0.8125rem; font-weight: 500; color: #4B5563; cursor: pointer; flex: 1; line-height: 1.2; }
        .toggle-switch { position: relative; display: inline-block; width: 2.5rem; height: 1.25rem; flex-shrink: 0; } .toggle-switch input { opacity: 0; width: 0; height: 0; }
        .toggle-slider { position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #E5E7EB; transition: all 0.2s ease-in-out; border-radius: 9999px; } .toggle-slider:before { position: absolute; content: ""; height: 0.9375rem; width: 0.9375rem; left: 0.15625rem; bottom: 0.15625rem; background-color: white; transition: all 0.2s ease-in-out; border-radius: 50%; box-shadow: 0 1px 3px 0 rgba(0,0,0,0.1),0 1px 2px 0 rgba(0,0,0,0.06); }
        input:checked+.toggle-slider { background-color: #3B82F6; } input:checked+.toggle-slider:before { transform: translateX(1.25rem); }
        .settings-notification { position: fixed; bottom: 11rem; right: 1.5rem; z-index: 9999; padding: 0.5rem 0.75rem; background-color: #3B82F6; color: white; border-radius: 0.375rem; font-size: 0.8125rem; animation: slideInOut 3s ease-in-out; }
        @keyframes slideInOut { 0%, 100% { opacity: 0; transform: translateX(20px); } 15%, 85% { opacity: 1; transform: translateX(0); } }
      `,
    },
    _settingsDefinition: [],
    init() {
      this._settingsDefinition = [
        { key: this._config.KEYS.SCRIPT_ENABLED, label: 'ページ最適化', defaultValue: true, handler: this._handleScriptToggle.bind(this) },
        { key: this._config.KEYS.FURIGANA_VISIBLE, label: '振り仮名表示', defaultValue: true, handler: this._handleFuriganaToggle.bind(this) },
        { key: this._config.KEYS.IFRAME_LAZY_LOAD, label: 'iFrame ロード', defaultValue: true, handler: this._handleIframeStrategyToggle.bind(this) },
      ]
      GM_addStyle(this._config.STYLES)
      this._createPanel()
      this._initializeFuriganaDisplay()
    },
    isScriptEnabled: () => GM_getValue(SettingsPanel._config.KEYS.SCRIPT_ENABLED, true),
    isLazyLoadEnabled: () => GM_getValue(SettingsPanel._config.KEYS.IFRAME_LAZY_LOAD, true),
    _get: (k, d) => GM_getValue(k, d),
    _set: (k, v) => GM_setValue(k, v),
    _showNotification(message = '設定を保存しました。再読み込みしてください。') {
      const el = document.createElement('div')
      el.className = 'settings-notification'
      el.textContent = message
      document.body.appendChild(el)
      setTimeout(() => el.remove(), 2800)
    },
    _handleScriptToggle(enabled) {
      this._set(this._config.KEYS.SCRIPT_ENABLED, enabled)
      this._showNotification()
    },
    _handleFuriganaToggle(visible) {
      this._set(this._config.KEYS.FURIGANA_VISIBLE, visible)
    },
    _handleIframeStrategyToggle(enabled) {
      this._set(this._config.KEYS.IFRAME_LAZY_LOAD, enabled)
      this._showNotification()
    },
    toggleFuriganaDisplay(visible) {
      const id = 'furigana-display-style'
      let style = document.getElementById(id)
      if (!style) {
        style = document.createElement('style')
        style.id = id
        document.head.appendChild(style)
      }
      style.textContent = `rt { display: ${visible ? 'ruby-text' : 'none'} !important; }`
    },
    _initializeFuriganaDisplay() {
      if (!this._get(this._config.KEYS.FURIGANA_VISIBLE, true)) this.toggleFuriganaDisplay(false)
    },
    _createPanel() {
      const panel = document.createElement('div')
      panel.id = 'settings-panel'
      panel.innerHTML = `<h3 class="settings-title">設定パネル</h3>`
      this._settingsDefinition.forEach((s) => panel.appendChild(this._createToggle(s)))
      document.body.appendChild(panel)
    },
    _createToggle(config) {
      const id = `setting-${config.key}`
      const cont = document.createElement('div')
      cont.className = 'setting-item'
      cont.innerHTML = `<label for="${id}" class="setting-label">${config.label}</label><label class="toggle-switch"><input type="checkbox" id="${id}" ${this._get(config.key, config.defaultValue) ? 'checked' : ''}><span class="toggle-slider"></span></label>`
      cont.querySelector('input').addEventListener('change', (e) => config.handler(e.target.checked))
      return cont
    },
  }
  const MainController = {
    run() {
      if (!SettingsPanel.isScriptEnabled()) {
        document.addEventListener('DOMContentLoaded', () => SettingsPanel.init())
        return
      }
      PageOptimizer.init()
      RubyConverter.init(RULES)
      document.addEventListener('DOMContentLoaded', () => {
        IframeLazyLoader.init()
        this._processPageContent()
        SettingsPanel.init()
      })
    },
    _processPageContent() {
      const articleBodies = document.querySelectorAll('.article-body-inner')
      if (articleBodies.length === 0) return
      let currentIndex = 0
      const processBatch = () => {
        const batchSize = Math.min(2, articleBodies.length - currentIndex)
        const endIndex = currentIndex + batchSize
        for (let i = currentIndex; i < endIndex; i++) {
          const body = articleBodies[i]
          IframeLazyLoader.processContainer(body)
          ImageProcessor.process(body)
          RubyConverter.processContainer(body)
          PageOptimizer.cleanupArticleBody(body)
        }
        currentIndex = endIndex
        if (currentIndex < articleBodies.length) {
          requestAnimationFrame(processBatch)
        } else {
          PageOptimizer.finalizeLayout()
        }
      }
      requestAnimationFrame(processBatch)
    },
  }
  MainController.run()
})()
