{"name": "tampermonkey", "description": "A Tampermonkey script for enhancing the reading experience on edewakaru.com", "version": "1.0.0", "private": true, "author": "iPumpkin <https://github.com/ipumpkin17>", "type": "module", "scripts": {"sort": "node script/sort-japanese-words.js -i src/edewakaru.js", "clean": "node script/clean-userscript.js -i src/edewakaru.js"}, "dependencies": {"@babel/generator": "^7.28.0", "@babel/parser": "^7.28.0", "@babel/traverse": "^7.28.0", "chalk": "^5.4.1", "prettier": "^3.6.2", "yargs": "^18.0.0"}}