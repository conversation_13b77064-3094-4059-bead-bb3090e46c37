{"name": "get-east-asian-width", "version": "1.3.0", "description": "Determine the East Asian Width of a Unicode character", "license": "MIT", "repository": "sindresorhus/get-east-asian-width", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsc index.d.ts", "build": "node scripts/build.js", "prepublish": "npm run build"}, "files": ["index.js", "index.d.ts", "lookup.js"], "keywords": ["unicode", "east-asian-width", "eastasianwidth", "character", "string", "width", "text", "layout", "alignment", "fullwidth", "halfwidth", "ambiguous", "narrow", "wide", "neutral", "typography", "japanese", "chinese", "korean", "codepoint", "text-processing", "i18n", "l10n"], "devDependencies": {"ava": "^5.3.1", "indent-string": "^5.0.0", "outdent": "^0.8.0", "simplify-ranges": "^0.1.0", "typescript": "^5.2.2", "xo": "^0.56.0"}, "xo": {"ignores": ["lookup.js"]}}