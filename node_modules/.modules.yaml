hoistPattern:
  - '*'
hoistedLocations:
  '@babel/code-frame@7.27.1':
    - node_modules/@babel/code-frame
  '@babel/generator@7.28.0':
    - node_modules/@babel/generator
  '@babel/helper-globals@7.28.0':
    - node_modules/@babel/helper-globals
  '@babel/helper-string-parser@7.27.1':
    - node_modules/@babel/helper-string-parser
  '@babel/helper-validator-identifier@7.27.1':
    - node_modules/@babel/helper-validator-identifier
  '@babel/parser@7.28.0':
    - node_modules/@babel/parser
  '@babel/template@7.27.2':
    - node_modules/@babel/template
  '@babel/traverse@7.28.0':
    - node_modules/@babel/traverse
  '@babel/types@7.28.0':
    - node_modules/@babel/types
  '@jridgewell/gen-mapping@0.3.12':
    - node_modules/@jridgewell/gen-mapping
  '@jridgewell/resolve-uri@3.1.2':
    - node_modules/@jridgewell/resolve-uri
  '@jridgewell/sourcemap-codec@1.5.4':
    - node_modules/@jridgewell/sourcemap-codec
  '@jridgewell/trace-mapping@0.3.29':
    - node_modules/@jridgewell/trace-mapping
  ansi-regex@6.1.0:
    - node_modules/ansi-regex
  ansi-styles@6.2.1:
    - node_modules/ansi-styles
  chalk@5.4.1:
    - node_modules/chalk
  cliui@9.0.1:
    - node_modules/cliui
  debug@4.4.1:
    - node_modules/debug
  emoji-regex@10.4.0:
    - node_modules/emoji-regex
  escalade@3.2.0:
    - node_modules/escalade
  get-caller-file@2.0.5:
    - node_modules/get-caller-file
  get-east-asian-width@1.3.0:
    - node_modules/get-east-asian-width
  js-tokens@4.0.0:
    - node_modules/js-tokens
  jsesc@3.1.0:
    - node_modules/jsesc
  ms@2.1.3:
    - node_modules/ms
  picocolors@1.1.1:
    - node_modules/picocolors
  prettier@3.6.2:
    - node_modules/prettier
  string-width@7.2.0:
    - node_modules/string-width
  strip-ansi@7.1.0:
    - node_modules/strip-ansi
  wrap-ansi@9.0.0:
    - node_modules/wrap-ansi
  y18n@5.0.8:
    - node_modules/y18n
  yargs-parser@22.0.0:
    - node_modules/yargs-parser
  yargs@18.0.0:
    - node_modules/yargs
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: hoisted
packageManager: pnpm@10.12.4
pendingBuilds: []
prunedAt: Wed, 09 Jul 2025 05:58:34 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmmirror.com/
skipped: []
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
