#!/usr/bin/env node
// 脚本：sort-japanese-words.js
// 功能：按照日语五十音图顺序排序词汇列表
// 用法：node sort-japanese-words.js

const japaneseOrder =
  "あいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもやゆよらりるれろわをんがぎぐげござじずぜぞだぢづでどばびぶべぼぱぴぷぺぽぁぃぅぇぉゃゅょっ";

// 获取词汇的读音（括号内的假名）
function getReading(word) {
  const match = word.match(/（([^）]+)）/);
  return match ? match[1] : word;
}

// 比较两个日语词汇的五十音顺序
function compareJapanese(a, b) {
  const readingA = getReading(a);
  const readingB = getReading(b);

  const maxLength = Math.max(readingA.length, readingB.length);

  for (let i = 0; i < maxLength; i++) {
    const charA = readingA[i] || "";
    const charB = readingB[i] || "";

    if (charA === charB) continue;

    const indexA = japaneseOrder.indexOf(charA);
    const indexB = japaneseOrder.indexOf(charB);

    // 如果字符在排序表中，按表顺序比较
    if (indexA !== -1 && indexB !== -1) {
      return indexA - indexB;
    }

    // 如果只有一个字符在表中，在表中的字符优先
    if (indexA !== -1) return -1;
    if (indexB !== -1) return 1;

    // 都不在表中，按 Unicode 顺序比较
    return charA.localeCompare(charB);
  }

  return 0;
}

// 主函数
function sortJapaneseWords(words) {
  return [...words].sort(compareJapanese);
}

// 示例词汇列表
const STANDARD_WORDS = [
  "長い間（ながいあいだ）",
  "座り心地（すわりごこち）",
  "触り心地（さわりごこち）",
  "申し訳（もうしわけ）",
  "出張（しゅっちょう）",
  "大好き（だいすき）",
  "唐揚げ（からあげ）",
  "立ち読み（たちよみ）",
  "１杯（いっぱい）",
  "１回（いっかい）",
  "１泊（いっぱく）",
  "１か月（いっかげつ）",
  "１か月間（いっかげつかん）",
  "試験（しけん）",
  "使用（しよう）",
  "前（まえ）",
  "待（ま）",
  "日記（にっき）",
  "話し手（はなして）",
  "聞き手（ききて）",
  "以上（いじょう）",
  "使い方（つかいかた）",
  "０点（れいてん）",
  "買い物（かいもの）",
  "動作（どうさ）",
  "m（メートル）",
  "味覚 （みかく）",
  "気持ち（きもち）",
  "青い色（あおいいろ）",
  "吐き気（はきけ）",
  "元カレ（もとかれ）",
  "髪の毛（かみのけ）",
  "駅（えき）",
  "万引き（まんびき）",
  "通（どお）",
  "遅刻（ちこく）",
  "経（た）",
  "三分の一（さんぶんのいち）",
  "折があれば（おりがあれば）",
  "折を見て（おりをみて）",
  "折に触れて（おりにふれて）",
  "折も折（おりもおり）",
  "残業（ざんぎょう）",
  "合（あ）",
  "楽（たの）",
  "貸し借り（かしかり）",
  "入学（にゅうがく）",
  "暮（ぐ）",
  "届け出（とどけで）",
  "有名（ゆうめい）",
  "自身（じしん）",
  "住（す）",
  "夕ご飯（ゆうごはん）",
  "星の数（ほしのかず）",
  "窓の外（まどのそと）",
  "考え方（かんがえかた）",
  "感じ方（かんじかた）",
  "貯（た）",
  "悩み事（なやみごと）",
  "歩（ある）",
  "食べず嫌い（たべずぎらい）",
  "アタック（attack）",
  "お茶する（おちゃする）",
  "入（はい）",
  "使い分け（つかいわけ）",
  "行き渡る（いきわたる）",
  "星の数ほどある（ほしのかずほどある）",
  "星の数ほどいる（ほしのかずほどいる）",
  "５日間（いつかかん）",
  "食べ物（たべもの）",
  "お団子（おだんご）",
  "足が早い（あしがはやい）",
  "昭和の日（しょうわのひ）",
  "思い出す（おもいだす）",
  "甘い物（あまいもの）",
  "呼び方（よびかた）",
  "真っ暗（まっくら）",
  "寝る前（ねるまえ）",
  "独り言（ひとりごと）",
  "口の中（くちのなか）",
  "雨の日（あめのひ）",
  "行き方（いきかた）",
  "腹が立つ（はらがたつ）",
  "文法（ぶんぽう）",
  "当たり前（あたりまえ）",
  "盗み食い（ぬすみぐい）",
  "落ち着（おちつ）",
  "夏休み中（なつやすみちゅう）",
  "連続（れんぞく）",
  "一つ（ひとつ）",
  "寝（ね）",
];

// 排序并输出结果
const sortedWords = sortJapaneseWords(STANDARD_WORDS);
console.log("STANDARD_WORDS: [");
sortedWords.forEach((word, index) => {
  const comma = index < sortedWords.length - 1 ? "," : "";
  console.log(`  '${word}'${comma}`);
});
console.log("]");
